<template>
  <div>
    <el-select
        class="width185"
        v-model="selectValue"
        autocomplete
        :clearable="clearable"
        :disabled="disabled"
        :placeholder="placeholder"
        filterable
        style="width: 100%"
        :multiple="multiple"
        :multiple-limit="multipleLimit"
        @change="handleChange">
      <el-option
        v-for="item in options"
        :key="item[props.value]"
        :label="item[props.label]"
        :value="item[props.value]">
      </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: 'el-dict',
    componentName: 'ElDict',
    data () {
      return {
        options: [],
        selectValue: ''
      }
    },
    props: {
      disabled: {
        type: Boolean,
        default: false
      },
      clearable: {
        type: Boolean,
        default: true
      },
      // 导入的url地址（兼容旧版本）
      code: {
        type: String
      },
      // 自定义请求URL
      customUrl: {
        type: String,
        default: ''
      },
      // 自定义请求参数
      customParams: {
        type: Object,
        default: () => ({})
      },
      // 字段映射配置对象
      props: {
        type: Object,
        default: () => ({
          label: 'name',  // 兼容旧版本
          value: 'code'   // 兼容旧版本
        })
      },
      // 接受外部v-model传入的值，必须使用value
      value: {
      },
      multiple: {
        type: Boolean,
        default: false
      },
      // 多选数量限制
      multipleLimit: {
        type: Number,
        default: 0  // 0表示无限制
      },
      placeholder: {
        type: String,
        default: '请选择'
      },
      // 是否默认选中第一个选项
      defaultFirst: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      // 判断下拉框的值是否有改变
      selectValue (val, oldVal) {
        // 对于数组类型（多选），需要深度比较
        let isChanged = false
        if (Array.isArray(val) && Array.isArray(oldVal)) {
          isChanged = JSON.stringify(val) !== JSON.stringify(oldVal)
        } else {
          isChanged = val !== oldVal
        }

        if (isChanged) {
          // 只发出 input 事件，验证由 handleChange 处理
          this.$emit('input', this.selectValue)
        }
      },
      value (val) {
        if (typeof val === 'number') {
          this.selectValue = val.toString()
        } else {
          this.selectValue = val
        }
      }
    },
    mounted () {
      // 远程请求回来的数据
      this.selectValue = this.value
      this.loadData()
    },
    methods: {
      // Element UI 内部方法，用于向父组件传递事件
      dispatch(componentName, eventName, params) {
        var parent = this.$parent || this.$root;
        var name = parent.$options.componentName;

        while (parent && (!name || name !== componentName)) {
          parent = parent.$parent;

          if (parent) {
            name = parent.$options.componentName;
          }
        }
        if (parent) {
          parent.$emit.apply(parent, [eventName].concat(params));
        }
      },
      // 触发表单验证
      triggerValidation() {
        // 获取当前的实际值
        const currentValue = this.selectValue
        console.log('Triggering validation with value:', currentValue)

        // 查找父级 ElFormItem 组件
        let formItem = this.$parent
        while (formItem) {
          if (formItem.$options.componentName === 'ElFormItem') {
            break
          }
          formItem = formItem.$parent
        }

        if (formItem) {
          // 查找 ElForm 组件
          let form = formItem.$parent
          while (form) {
            if (form.$options.componentName === 'ElForm') {
              break
            }
            form = form.$parent
          }

          if (form && formItem.prop) {
            const fieldValue = form.model[formItem.prop]
            console.log('Form data before validation:', fieldValue)
            console.log('Field value type:', typeof fieldValue)
            console.log('Field value length:', Array.isArray(fieldValue) ? fieldValue.length : 'not array')
            console.log('Field value content:', JSON.stringify(fieldValue))

            // 等待一个更长的时间确保数据已更新
            setTimeout(() => {
              const delayedValue = form.model[formItem.prop]
              console.log('Form data after delay:', delayedValue)
              console.log('Delayed value type:', typeof delayedValue)
              console.log('Delayed value length:', Array.isArray(delayedValue) ? delayedValue.length : 'not array')
              console.log('Delayed value content:', JSON.stringify(delayedValue))

              // 尝试清理响应式包装
              if (Array.isArray(delayedValue) && delayedValue.length > 0) {
                // 创建一个纯数组副本
                const cleanArray = [...delayedValue]
                console.log('Clean array:', cleanArray)
                // 临时设置为纯数组
                form.model[formItem.prop] = cleanArray

                // 延迟一点再验证
                setTimeout(() => {
                  form.validateField(formItem.prop)
                }, 50)
              } else {
                // 手动触发字段验证
                form.validateField(formItem.prop)
              }
            }, 200)
          }
        }
      },
      // 处理选择变更
      handleChange(value) {
        // 发出 change 事件并触发验证
        this.$emit('change', value)
        // 触发表单验证
        this.$nextTick(() => {
          this.dispatch('ElFormItem', 'el.form.change', [value])
        })
      },
      loadData() {
        let url, params

        if (this.customUrl) {
          // 使用自定义URL和参数
          url = this.$http.adornUrl(this.customUrl)
          params = this.$http.adornParams(this.customParams)
        } else {
          // 兼容旧版本，使用默认的字典接口
          url = this.$http.adornUrl(`/admin/dict/parent`)
          params = this.$http.adornParams({ code: this.code })
        }

        this.$http({
          url: url,
          method: 'get',
          params: params
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.options = data.obj || []
            // 如果启用了默认选中第一个选项，且当前没有值，则选中第一个
            if (this.defaultFirst && !this.selectValue && this.options.length > 0) {
              this.selectValue = this.options[0][this.props.value]
              this.$emit('input', this.selectValue)
            }
            this.$emit('filterDictData')
          }
        })
      }
    }
  }
</script>
